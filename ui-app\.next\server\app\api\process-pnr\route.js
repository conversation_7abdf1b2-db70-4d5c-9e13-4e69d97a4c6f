/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/process-pnr/route";
exports.ids = ["app/api/process-pnr/route"];
exports.modules = {

/***/ "(rsc)/./constants.js":
/*!**********************!*\
  !*** ./constants.js ***!
  \**********************/
/***/ ((module) => {

"use strict";
eval("/**\n * constants.js - Application constants and mappings\n * \n * This file contains all the constant values used throughout the application,\n * including status codes, channel codes, and other configuration values.\n */ // Status code mapping for FlyDubai reservation statuses\n\nconst statusCodeMap = {\n    0: \"CANCELED\",\n    1: \"ACTIVE\",\n    2: \"WAIT LIST\",\n    3: \"STAND BY\",\n    4: \"CHECKED-IN\",\n    5: \"BOARDED\",\n    6: \"FLYING\",\n    7: \"FLOWN\",\n    8: \"HOLD\",\n    9: \"NO SHOW\",\n    10: \"CONFIRM\",\n    11: \"PENDING\"\n};\n// Channel code mapping for FlyDubai booking channels\nconst channelCodeMap = {\n    1: \"STANDARD\",\n    2: \"WEB\",\n    3: \"GDS\",\n    4: \"SYNCBOX\",\n    5: \"TPAPI\",\n    6: \"EDIFACT\",\n    7: \"DCS\",\n    8: \"EXT_WEB\",\n    11: \"PNRIMPORT\",\n    12: \"MOBILE\",\n    13: \"MyID\",\n    15: \"IATCI\",\n    16: \"TA\",\n    17: \"HOLIDAYS\",\n    18: \"CORPORATE\",\n    19: \"CHECKINDESK\",\n    20: \"OLCI\",\n    21: \"KIOSK\",\n    22: \"INCOMING_MESSAGE\",\n    23: \"SYSTEM\",\n    25: \"ASCONNECT\",\n    26: \"REACCOM\",\n    27: \"RES\",\n    28: \"PLUSGRADE\",\n    29: \"STAFF_TRAVEL\",\n    30: \"ARMS\",\n    31: \"EBOARDING\",\n    32: \"AUTOCHECKIN\",\n    33: \"GMS\",\n    35: \"SDCS_MOBILE\"\n};\n// Export all constants\nmodule.exports = {\n    statusCodeMap,\n    channelCodeMap\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./constants.js\n");

/***/ }),

/***/ "(rsc)/./functions.js":
/*!**********************!*\
  !*** ./functions.js ***!
  \**********************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/**\n * functions.js - Utility functions for extracting and processing policy information from PNR data\n * This file handles the extraction of Cover Genius policy IDs from FlyDubai PNR responses\n * and processes insurance data for report generation\n */ // Import required dependencies\n\nconst moment = __webpack_require__(/*! moment */ \"(rsc)/./node_modules/moment/moment.js\");\nconst { statusCodeMap, channelCodeMap } = __webpack_require__(/*! ./constants */ \"(rsc)/./constants.js\");\n/**\n * Extracts Cover Genius policy IDs from the PNR data\n * @param {Object} pnrData - The parsed PNR data object\n * @returns {Array} - Array of policy IDs\n */ function extractCoverGeniusPolicyIds(pnrData) {\n    const policyIds = [];\n    const seen = new Set(); // To prevent duplicate policy IDs\n    // Check each paxSegment for insurance information\n    if (pnrData && pnrData.paxSegments && Array.isArray(pnrData.paxSegments)) {\n        pnrData.paxSegments.forEach((paxSegment)=>{\n            if (paxSegment.recordDetails && paxSegment.recordDetails.length > 0) {\n                paxSegment.recordDetails.forEach((record)=>{\n                    if (record.insuTransID && (!record.provider || record.provider !== 'AIG')) {\n                        // Extract the policy ID from the insurance transaction ID\n                        const parts = record.insuTransID.split('/');\n                        if (parts.length > 0) {\n                            const policyId = parts[0];\n                            // Add to array if not already added\n                            if (!seen.has(policyId)) {\n                                policyIds.push(policyId);\n                                seen.add(policyId);\n                                console.log(`Found Cover Genius policy ID: ${policyId}`);\n                            }\n                        }\n                    }\n                });\n            }\n        });\n    }\n    return policyIds;\n}\n/**\n * Processes insurance records from PNR data and Cover Genius policy data\n * @param {Object} pnrData - The PNR data from FlyDubai API\n * @param {Object} policyData - The policy data from Cover Genius API\n * @param {string} policyId - The policy ID\n * @returns {Object} - Processed data for report generation\n */ function processInsuranceData(pnrData, policyData, policyId) {\n    const insuranceRecords = [];\n    // Extract policy dates from Cover Genius response\n    let policyStartDate = null;\n    let policyEndDate = null;\n    // Handle different possible Cover Genius response structures\n    if (policyData && policyData.data) {\n        // Try different possible structures\n        if (policyData.data.quotes && policyData.data.quotes.length > 0) {\n            // Structure: data.quotes[0].policy_start_date\n            const quote = policyData.data.quotes[0];\n            if (quote.policy_start_date) policyStartDate = moment(quote.policy_start_date);\n            if (quote.policy_end_date) policyEndDate = moment(quote.policy_end_date);\n        } else if (policyData.data.policy) {\n            // Structure: data.policy.start_date\n            const policy = policyData.data.policy;\n            if (policy.start_date) policyStartDate = moment(policy.start_date);\n            if (policy.end_date) policyEndDate = moment(policy.end_date);\n        }\n    } else if (policyData && policyData.quotes && policyData.quotes.length > 0) {\n        // Direct quotes structure\n        const quote = policyData.quotes[0];\n        if (quote.policy_start_date) policyStartDate = moment(quote.policy_start_date);\n        if (quote.policy_end_date) policyEndDate = moment(quote.policy_end_date);\n    }\n    // Function to check if a date falls within policy period\n    function isWithinPolicyPeriod(date) {\n        if (!date || !policyStartDate || !policyEndDate) return \"Unknown\";\n        const momentDate = moment(date);\n        return momentDate.isSameOrAfter(policyStartDate) && momentDate.isSameOrBefore(policyEndDate);\n    }\n    // Create a mapping of passengers\n    const passengersMap = {};\n    if (pnrData.persons && Array.isArray(pnrData.persons)) {\n        pnrData.persons.forEach((person)=>{\n            passengersMap[person.paxID] = {\n                fullName: `${person.title || ''} ${person.fName || ''} ${person.lName || ''}`.trim(),\n                recNums: person.recNum || []\n            };\n        });\n    }\n    // Build segment information\n    const segmentsMap = {};\n    if (pnrData.segments && Array.isArray(pnrData.segments)) {\n        pnrData.segments.forEach((segment)=>{\n            segmentsMap[segment.segKey] = {\n                origin: segment.org,\n                destination: segment.dest,\n                departureTime: segment.depTime,\n                arrivalTime: segment.arrTime,\n                flightNumber: `${segment.operCarrier}${segment.operFlightNum}`,\n                persons: segment.persons || []\n            };\n        });\n    }\n    // Create a map to link record numbers to segments\n    const recNumToSegmentMap = {};\n    if (pnrData.segments && Array.isArray(pnrData.segments)) {\n        pnrData.segments.forEach((segment)=>{\n            if (segment.persons && Array.isArray(segment.persons)) {\n                segment.persons.forEach((person)=>{\n                    recNumToSegmentMap[person.recNum] = segment;\n                });\n            }\n        });\n    }\n    // Build a mapping between recNum and person IDs\n    const recNumToPersonMap = {};\n    if (pnrData.persons && Array.isArray(pnrData.persons)) {\n        pnrData.persons.forEach((person)=>{\n            if (person.recNum && Array.isArray(person.recNum)) {\n                person.recNum.forEach((recNum)=>{\n                    recNumToPersonMap[recNum] = person.paxID;\n                });\n            }\n        });\n    }\n    // Process paxSegments data to find ALL records with insurance\n    console.log(`Processing ${pnrData.paxSegments ? pnrData.paxSegments.length : 0} paxSegments for insurance records...`);\n    if (pnrData.paxSegments && Array.isArray(pnrData.paxSegments)) {\n        pnrData.paxSegments.forEach((paxSegment, segmentIndex)=>{\n            if (paxSegment.recordDetails && paxSegment.recordDetails.length > 0) {\n                console.log(`  Segment ${segmentIndex + 1} (recNum: ${paxSegment.recNum}): Found ${paxSegment.recordDetails.length} record details`);\n                paxSegment.recordDetails.forEach((record, recordIndex)=>{\n                    // Include ALL insurance records - check for any insurance-related fields\n                    // This ensures we capture all insurance-related records in the PNR\n                    const hasInsuranceData = record.insuTransID || record.provider || record.insuConfNum || record.insuPurchasedate || record.provider && record.provider.toLowerCase().includes('insurance') || record.provider && record.provider.toLowerCase().includes('cover') || record.provider && record.provider.toLowerCase().includes('aig') || record.insuTransID && record.insuTransID.trim() !== '';\n                    if (hasInsuranceData) {\n                        console.log(`    Record ${recordIndex + 1}: Found insurance data - insuTransID: ${record.insuTransID || 'N/A'}, provider: ${record.provider || 'N/A'}`);\n                        // Find passenger ID from record number\n                        const paxId = recNumToPersonMap[paxSegment.recNum];\n                        // Build full insurance record with relevant information\n                        const insuranceInfo = {\n                            recordNumber: paxSegment.recNum,\n                            insuTransID: record.insuTransID || \"N/A\",\n                            insuConfNum: record.insuConfNum || null,\n                            statusCode: record.status,\n                            statusText: statusCodeMap[record.status] || \"Unknown\",\n                            channelCode: record.channelID,\n                            channelText: channelCodeMap[record.channelID] || \"Unknown\",\n                            provider: record.provider || \"Unknown\",\n                            insuPurchaseDate: record.insuPurchasedate ? moment(record.insuPurchasedate, \"M/D/YYYY h:mm:ss A\").format(\"YYYY-MM-DD HH:mm:ss\") : null,\n                            paxId: paxId,\n                            passengerName: paxId && passengersMap[paxId]?.fullName || \"Unknown\",\n                            bookDate: record.bookDate,\n                            hasConfirmation: !!record.insuConfNum,\n                            segmentInfo: null,\n                            departureDate: null,\n                            withinPolicyPeriod: \"Unknown\",\n                            matchesCoverGeniusPolicyId: record.insuTransID ? record.insuTransID.includes(policyId) : false\n                        };\n                        // Get segment info from recNum\n                        const segment = recNumToSegmentMap[paxSegment.recNum];\n                        if (segment) {\n                            // Format segment info in the expected structure\n                            insuranceInfo.segmentInfo = {\n                                origin: segment.org,\n                                destination: segment.dest,\n                                departureTime: segment.depTime,\n                                arrivalTime: segment.arrTime,\n                                flightNumber: `${segment.operCarrier}${segment.operFlightNum}`\n                            };\n                            insuranceInfo.departureDate = segment.depTime;\n                        }\n                        // Check if date is within policy period\n                        insuranceInfo.withinPolicyPeriod = isWithinPolicyPeriod(insuranceInfo.departureDate);\n                        insuranceRecords.push(insuranceInfo);\n                    }\n                });\n            }\n        });\n    }\n    console.log(`Total insurance records found: ${insuranceRecords.length}`);\n    if (insuranceRecords.length > 0) {\n        console.log('Insurance record providers:', [\n            ...new Set(insuranceRecords.map((r)=>r.provider))\n        ].join(', '));\n    }\n    return {\n        insuranceRecords,\n        policyStartDate: policyStartDate || moment(),\n        policyEndDate: policyEndDate || moment()\n    };\n}\n// Export all functions for use in other modules\nmodule.exports = {\n    extractCoverGeniusPolicyIds,\n    processInsuranceData\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./functions.js\n");

/***/ }),

/***/ "(rsc)/./integrations.js":
/*!*************************!*\
  !*** ./integrations.js ***!
  \*************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("// Load environment variables\n\n(__webpack_require__(/*! dotenv */ \"(rsc)/./node_modules/dotenv/lib/main.js\").config)();\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst path = __webpack_require__(/*! path */ \"path\");\nconst axios = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/dist/node/axios.cjs\");\n/**\n * Fetches policy details from Cover Genius API using a policy ID\n * @param {string} policyId - The policy ID to retrieve (format: XXXXX-XXXXX-XXX)\n * @returns {Promise} - Promise resolving to policy data\n */ function fetchCoverGeniusPolicy(policyId) {\n    const CryptoJS = __webpack_require__(/*! crypto-js */ \"(rsc)/./node_modules/crypto-js/index.js\");\n    const axios = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/dist/node/axios.cjs\");\n    // Get credentials and configuration from environment variables\n    const credentials = {\n        api_key: process.env.COVER_GENIUS_API_KEY,\n        api_secret: process.env.COVER_GENIUS_API_SECRET\n    };\n    const partnerId = process.env.COVER_GENIUS_PARTNER_ID;\n    const baseUrl = process.env.COVER_GENIUS_API_BASE_URL;\n    // Validate environment variables\n    if (!credentials.api_key || !credentials.api_secret || !partnerId || !baseUrl) {\n        throw new Error('Missing required Cover Genius environment variables. Please check your .env file.');\n    }\n    // Generate authentication headers\n    const date = new Date().toUTCString();\n    const signatureContentString = 'date: ' + date;\n    const signatureString = CryptoJS.HmacSHA1(signatureContentString, credentials.api_secret).toString(CryptoJS.enc.Base64);\n    const authHeader = 'Signature keyId=\"' + credentials.api_key + '\",algorithm=\"hmac-sha1\",signature=\"' + encodeURIComponent(signatureString) + '\"';\n    // Set request options\n    const options = {\n        method: 'GET',\n        url: `${baseUrl}/${partnerId}/bookings/${policyId}`,\n        headers: {\n            'X-Api-Key': credentials.api_key,\n            'Authorization': authHeader,\n            'Content-Type': 'application/json',\n            'Date': date\n        }\n    };\n    // Make the request and save response to file\n    return axios(options).then((response)=>{\n        try {\n            // Ensure data directory exists\n            const dataDir = path.join(__dirname, 'data');\n            if (!fs.existsSync(dataDir)) {\n                fs.mkdirSync(dataDir, {\n                    recursive: true\n                });\n            }\n            // Save the Cover Genius response to a file with policy ID in filename\n            const filename = `CoverGenius-${policyId}.json`;\n            const filePath = path.join(dataDir, filename);\n            fs.writeFileSync(filePath, JSON.stringify(response.data, null, 2));\n            console.log(`Saved Cover Genius policy data to data/${filename}`);\n        } catch (saveError) {\n            console.warn(`Warning: Could not save Cover Genius response to file: ${saveError.message}`);\n        }\n        return response;\n    }).catch((error)=>{\n        // Enhanced error logging for Cover Genius API failures\n        console.error(`❌ Cover Genius API call failed for policy ID: ${policyId}`);\n        console.error('Error details:', {\n            message: error.message,\n            status: error.response?.status,\n            statusText: error.response?.statusText,\n            url: error.config?.url,\n            method: error.config?.method\n        });\n        // Log the full error response if available\n        if (error.response?.data) {\n            console.error('Cover Genius API error response:', JSON.stringify(error.response.data, null, 2));\n            // Save error response to file for debugging\n            try {\n                const dataDir = path.join(__dirname, 'data');\n                if (!fs.existsSync(dataDir)) {\n                    fs.mkdirSync(dataDir, {\n                        recursive: true\n                    });\n                }\n                const errorFilename = `CoverGenius-ERROR-${policyId}-${Date.now()}.json`;\n                const errorFilePath = path.join(dataDir, errorFilename);\n                fs.writeFileSync(errorFilePath, JSON.stringify({\n                    policyId,\n                    timestamp: new Date().toISOString(),\n                    error: {\n                        message: error.message,\n                        status: error.response?.status,\n                        statusText: error.response?.statusText,\n                        data: error.response?.data\n                    }\n                }, null, 2));\n                console.log(`Saved Cover Genius error response to data/${errorFilename}`);\n            } catch (saveError) {\n                console.warn(`Warning: Could not save Cover Genius error response to file: ${saveError.message}`);\n            }\n        }\n        // Re-throw the error to ensure the transaction fails\n        throw new Error(`Cover Genius API failed for policy ${policyId}: ${error.message}${error.response?.status ? ` (HTTP ${error.response.status})` : ''}`);\n    });\n}\n/**\n * Fetches a security token from the FlyDubai API\n * @returns {Promise<string>} - Promise resolving to the security token\n */ async function getSecurityToken() {\n    try {\n        // Get credentials from environment variables\n        const baseUrl = process.env.FZ_API_BASE_URL;\n        const username = process.env.FZ_USERNAME;\n        const password = process.env.FZ_PASSWORD;\n        // Validate environment variables\n        if (!baseUrl || !username || !password) {\n            throw new Error('Missing required FlyDubai API environment variables. Please check your .env file.');\n        }\n        const response = await axios.post(`${baseUrl}/security/token`, {\n            userName: username,\n            password: password\n        }, {\n            headers: {\n                'Content-Type': 'application/json'\n            }\n        });\n        if (response.data && response.data.token) {\n            return response.data.token;\n        } else {\n            throw new Error('Token not found in response');\n        }\n    } catch (error) {\n        console.error('Error fetching security token:', error.message);\n        throw error;\n    }\n}\n/**\n * Fetches PNR data from the FlyDubai API\n * @param {string} pnrNumber - The PNR number to fetch\n * @param {string} token - The security token for authentication\n * @returns {Promise<object>} - Promise resolving to the PNR data\n */ async function fetchPNRData(pnrNumber, token) {\n    try {\n        const baseUrl = process.env.FZ_API_BASE_URL;\n        if (!baseUrl) {\n            throw new Error('Missing FZ_API_BASE_URL environment variable');\n        }\n        const response = await axios.post(`${baseUrl}/reservations/${pnrNumber}`, {\n            transactionDetails: {\n                clientIPAddress: \"127.0.0.1\",\n                userName: \"\"\n            },\n            filterDetails: {\n                DisplayReservation: true,\n                DisplayPayments: true,\n                DisplayOAFlights: false,\n                DisplayComments: false,\n                DisplayHistory: false,\n                DisplaySegments: true,\n                DisplayPersons: true,\n                DisplaySegmentPersonMap: true,\n                DisplaySeatAssignment: false,\n                DisplayAPISInfo: false,\n                DisplayPriceInfo: false,\n                DisplayCharges: true,\n                DisplayResPaymentMap: true,\n                DisplayPhysicalFlights: true,\n                DisplayReservationContacts: false,\n                DisplayContactInfos: false\n            }\n        }, {\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': token\n            }\n        });\n        return response.data;\n    } catch (error) {\n        console.error('Error fetching PNR data:', error.message);\n        throw error;\n    }\n}\n/**\n * Retrieves PNR data and saves it to files\n * @param {string} pnrNumber - The PNR number to retrieve\n * @returns {Promise<object>} - Promise resolving to the PNR data\n */ async function retrieveAndSavePNRData(pnrNumber) {\n    try {\n        console.log(`Fetching data for PNR: ${pnrNumber}`);\n        // Get security token\n        const token = await getSecurityToken();\n        console.log('Security token obtained successfully');\n        // Fetch PNR data\n        const retrievePNRData = await fetchPNRData(pnrNumber, token);\n        console.log('PNR data fetched successfully');\n        // Ensure data directory exists\n        const dataDir = path.join(__dirname, 'data');\n        if (!fs.existsSync(dataDir)) {\n            fs.mkdirSync(dataDir, {\n                recursive: true\n            });\n        }\n        // Save the PNR data to a file for reference with PNR number in filename\n        const pnrFileName = `RetrievePNR-${pnrNumber}.json`;\n        const pnrFilePath = path.join(dataDir, pnrFileName);\n        fs.writeFileSync(pnrFilePath, JSON.stringify(retrievePNRData, null, 2));\n        console.log(`✓ Saved PNR data to data/${pnrFileName}`);\n        console.log(`  - File size: ${(JSON.stringify(retrievePNRData).length / 1024).toFixed(2)} KB`);\n        return retrievePNRData;\n    } catch (error) {\n        console.error('❌ Error retrieving PNR data:', error.message);\n        // Log additional error details for debugging\n        if (error.response) {\n            console.error('  - HTTP Status:', error.response.status);\n            console.error('  - Response data:', error.response.data);\n        }\n        throw error;\n    }\n}\n// Export all functions for use in other modules\nmodule.exports = {\n    fetchCoverGeniusPolicy,\n    getSecurityToken,\n    fetchPNRData,\n    retrieveAndSavePNRData\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./integrations.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprocess-pnr%2Froute&page=%2Fapi%2Fprocess-pnr%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprocess-pnr%2Froute.ts&appDir=C%3A%5CUsers%5Cbasil.harrison%5CDocuments%5CCode%5Ccover-genius-v1%5Cui-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbasil.harrison%5CDocuments%5CCode%5Ccover-genius-v1%5Cui-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprocess-pnr%2Froute&page=%2Fapi%2Fprocess-pnr%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprocess-pnr%2Froute.ts&appDir=C%3A%5CUsers%5Cbasil.harrison%5CDocuments%5CCode%5Ccover-genius-v1%5Cui-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbasil.harrison%5CDocuments%5CCode%5Ccover-genius-v1%5Cui-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_basil_harrison_Documents_Code_cover_genius_v1_ui_app_src_app_api_process_pnr_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/process-pnr/route.ts */ \"(rsc)/./src/app/api/process-pnr/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/process-pnr/route\",\n        pathname: \"/api/process-pnr\",\n        filename: \"route\",\n        bundlePath: \"app/api/process-pnr/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\app\\\\api\\\\process-pnr\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_basil_harrison_Documents_Code_cover_genius_v1_ui_app_src_app_api_process_pnr_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprocess-pnr%2Froute&page=%2Fapi%2Fprocess-pnr%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprocess-pnr%2Froute.ts&appDir=C%3A%5CUsers%5Cbasil.harrison%5CDocuments%5CCode%5Ccover-genius-v1%5Cui-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbasil.harrison%5CDocuments%5CCode%5Ccover-genius-v1%5Cui-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/process-pnr/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/process-pnr/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Import functions directly from the application modules\nasync function processMainFunction(pnr) {\n    try {\n        // Import required functions\n        // eslint-disable-next-line @typescript-eslint/no-require-imports\n        const { retrieveAndSavePNRData, fetchCoverGeniusPolicy } = __webpack_require__(/*! ../../../../integrations.js */ \"(rsc)/./integrations.js\");\n        // eslint-disable-next-line @typescript-eslint/no-require-imports\n        const { extractCoverGeniusPolicyIds, processInsuranceData } = __webpack_require__(/*! ../../../../functions.js */ \"(rsc)/./functions.js\");\n        // eslint-disable-next-line @typescript-eslint/no-require-imports\n        const moment = __webpack_require__(/*! moment */ \"(rsc)/./node_modules/moment/moment.js\");\n        console.log(`Processing PNR: ${pnr}`);\n        // Step 1: Retrieve PNR data from FlyDubai API\n        console.log('Step 1: Retrieving PNR data from FlyDubai API...');\n        const pnrData = await retrieveAndSavePNRData(pnr);\n        console.log('✓ PNR data retrieved successfully');\n        // Step 2: Extract Cover Genius policy IDs\n        console.log('Step 2: Extracting Cover Genius policy IDs...');\n        const policyIds = extractCoverGeniusPolicyIds(pnrData);\n        if (policyIds.length === 0) {\n            console.log('❌ No Cover Genius policy IDs found in PNR data');\n            return {\n                pnrNumber: pnr,\n                policyId: null,\n                insuranceRecords: [],\n                policyStartDate: null,\n                policyEndDate: null,\n                summary: {\n                    totalRecords: 0,\n                    withConfirmation: 0,\n                    missingConfirmation: 0,\n                    withinPolicyPeriod: 0\n                },\n                sqlQueries: [],\n                error: 'No Cover Genius policy IDs found in PNR data'\n            };\n        }\n        console.log(`✓ Found ${policyIds.length} policy ID(s): ${policyIds.join(', ')}`);\n        // Step 3: Process the first policy ID\n        const policyId = policyIds[0];\n        console.log(`Step 3: Processing policy ID: ${policyId}`);\n        // Fetch Cover Genius policy data\n        console.log('  - Fetching policy details from Cover Genius API...');\n        let policyResponse;\n        try {\n            policyResponse = await fetchCoverGeniusPolicy(policyId);\n            console.log('  ✓ Policy data retrieved successfully');\n        } catch (coverGeniusError) {\n            const errorMessage = coverGeniusError instanceof Error ? coverGeniusError.message : 'Unknown Cover Genius API error';\n            console.error(`  ❌ Cover Genius API call failed for policy ${policyId}:`, errorMessage);\n            // Return error response with detailed information\n            return {\n                pnrNumber: pnr,\n                policyId,\n                insuranceRecords: [],\n                policyStartDate: null,\n                policyEndDate: null,\n                summary: {\n                    totalRecords: 0,\n                    withConfirmation: 0,\n                    missingConfirmation: 0,\n                    withinPolicyPeriod: 0\n                },\n                sqlQueries: [],\n                error: `Cover Genius API failed: ${errorMessage}`,\n                errorType: 'COVER_GENIUS_API_ERROR'\n            };\n        }\n        // Extract policy dates\n        let policyStartDate = null;\n        let policyEndDate = null;\n        if (policyResponse && policyResponse.data) {\n            if (policyResponse.data.quotes && policyResponse.data.quotes.length > 0) {\n                const quote = policyResponse.data.quotes[0];\n                if (quote.policy_start_date) policyStartDate = moment(quote.policy_start_date);\n                if (quote.policy_end_date) policyEndDate = moment(quote.policy_end_date);\n            } else if (policyResponse.data.policy) {\n                const policy = policyResponse.data.policy;\n                if (policy.start_date) policyStartDate = moment(policy.start_date);\n                if (policy.end_date) policyEndDate = moment(policy.end_date);\n            }\n        } else if (policyResponse && policyResponse.quotes && policyResponse.quotes.length > 0) {\n            const quote = policyResponse.quotes[0];\n            if (quote.policy_start_date) policyStartDate = moment(quote.policy_start_date);\n            if (quote.policy_end_date) policyEndDate = moment(quote.policy_end_date);\n        }\n        if (policyStartDate && policyEndDate) {\n            console.log(`  - Policy period: ${policyStartDate.format(\"YYYY-MM-DD HH:mm:ss\")} to ${policyEndDate.format(\"YYYY-MM-DD HH:mm:ss\")}`);\n        }\n        // Process the insurance data\n        console.log('  - Processing insurance data...');\n        const processedData = processInsuranceData(pnrData, policyResponse, policyId);\n        console.log(`  ✓ Processed ${processedData.insuranceRecords.length} insurance records`);\n        // Calculate summary statistics\n        const missingConfirmations = processedData.insuranceRecords.filter((record)=>!record.hasConfirmation);\n        const recordsWithMatchingPolicyId = processedData.insuranceRecords.filter((r)=>r.matchesCoverGeniusPolicyId);\n        const recordsWithinPolicyPeriod = processedData.insuranceRecords.filter((r)=>r.withinPolicyPeriod === true);\n        console.log(`  - Records missing confirmation: ${missingConfirmations.length}`);\n        console.log(`  - Records matching policy ID: ${recordsWithMatchingPolicyId.length}`);\n        console.log(`  - Records within policy period: ${recordsWithinPolicyPeriod.length}`);\n        // Generate SQL queries for missing confirmations\n        const sqlQueries = missingConfirmations.map((record)=>({\n                recordNumber: record.recordNumber,\n                passenger: record.passengerName,\n                query: `UPDATE P_FZ.RESERVATION_SEGS SET INSURANCE_CONF_NUM='${policyId}' WHERE CONFIRMATION_NUM='${pnr}' AND RECORD_NUM=${record.recordNumber};`\n            }));\n        // Return structured data\n        return {\n            pnrNumber: pnr,\n            policyId,\n            insuranceRecords: processedData.insuranceRecords,\n            policyStartDate: policyStartDate ? policyStartDate.format('YYYY-MM-DD') : null,\n            policyEndDate: policyEndDate ? policyEndDate.format('YYYY-MM-DD') : null,\n            summary: {\n                totalRecords: processedData.insuranceRecords.length,\n                withConfirmation: processedData.insuranceRecords.length - missingConfirmations.length,\n                missingConfirmation: missingConfirmations.length,\n                withinPolicyPeriod: recordsWithinPolicyPeriod.length\n            },\n            sqlQueries\n        };\n    } catch (error) {\n        console.error('Error in processMainFunction:', error);\n        console.error('Current working directory:', process.cwd());\n        throw error;\n    }\n}\nasync function POST(request) {\n    try {\n        const { pnr } = await request.json();\n        if (!pnr || typeof pnr !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'PNR is required and must be a string'\n            }, {\n                status: 400\n            });\n        }\n        // Validate PNR format\n        const cleanPnr = pnr.trim().toUpperCase();\n        if (cleanPnr.length < 3 || cleanPnr.length > 10) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'PNR must be between 3 and 10 characters'\n            }, {\n                status: 400\n            });\n        }\n        try {\n            const result = await processMainFunction(cleanPnr);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n        } catch (processingError) {\n            console.error('Error processing PNR:', processingError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to process PNR',\n                details: processingError instanceof Error ? processingError.message : 'Unknown processing error'\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/process-pnr/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/crypto-js","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/dotenv","vendor-chunks/axios","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/moment","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprocess-pnr%2Froute&page=%2Fapi%2Fprocess-pnr%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprocess-pnr%2Froute.ts&appDir=C%3A%5CUsers%5Cbasil.harrison%5CDocuments%5CCode%5Ccover-genius-v1%5Cui-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbasil.harrison%5CDocuments%5CCode%5Ccover-genius-v1%5Cui-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();