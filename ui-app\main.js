/**
 * Main Application - Cover Genius Policy Analysis
 *
 * This application orchestrates the complete workflow:
 * 1. Accepts a PNR number
 * 2. Retrieves PNR data from FlyDubai API
 * 3. Extracts Cover Genius policy IDs
 * 4. Fetches policy details from Cover Genius API
 * 5. Generates comprehensive HTML report
 */

// Load environment variables
require('dotenv').config();

// Import required modules
const { retrieveAndSavePNRData, fetchCoverGeniusPolicy } = require('./integrations');
const { extractCoverGeniusPolicyIds, processInsuranceData } = require('./functions');
const { generateHtmlReport } = require('./reports');
const moment = require('moment');

/**
 * Main function that orchestrates the entire workflow
 * @param {string} pnrNumber - The PNR number to process
 * @param {boolean} returnData - Whether to return structured data instead of logging
 */
async function main(pnrNumber, returnData = false) {
    try {
        if (!returnData) {
            console.log('='.repeat(60));
            console.log('Cover Genius Policy Analysis - Starting Process');
            console.log('='.repeat(60));
            console.log(`Processing PNR: ${pnrNumber}`);
            console.log('');
        }

        // Step 1: Retrieve PNR data
        if (!returnData) console.log('Step 1: Retrieving PNR data from flydubai API...');
        const pnrData = await retrieveAndSavePNRData(pnrNumber);
        if (!returnData) {
            console.log('✓ PNR data retrieved successfully');
            console.log('');
        }

        // Step 2: Extract Cover Genius policy IDs
        if (!returnData) console.log('Step 2: Extracting Cover Genius policy IDs...');
        const policyIds = extractCoverGeniusPolicyIds(pnrData);

        if (policyIds.length === 0) {
            if (!returnData) {
                console.log('❌ No Cover Genius policy IDs found in PNR data');
                console.log('Process completed - no policies to analyze');
            }
            if (returnData) {
                return {
                    pnrNumber,
                    policyId: null,
                    insuranceRecords: [],
                    policyStartDate: null,
                    policyEndDate: null,
                    summary: {
                        totalRecords: 0,
                        withConfirmation: 0,
                        missingConfirmation: 0,
                        withinPolicyPeriod: 0,
                    },
                    sqlQueries: [],
                    error: 'No Cover Genius policy IDs found in PNR data'
                };
            }
            return;
        }

        if (!returnData) {
            console.log(`✓ Found ${policyIds.length} policy ID(s): ${policyIds.join(', ')}`);
            console.log('');
        }

        // Step 3: Process each policy ID (for API, we'll process the first one)
        const policyId = policyIds[0]; // For API, process first policy only

        if (!returnData) {
            console.log(`Step 3.1: Processing policy ID: ${policyId}`);
        }

        try {
            // Fetch Cover Genius policy data
            if (!returnData) console.log('  - Fetching policy details from Cover Genius API...');
            let policyResponse;
            try {
                policyResponse = await fetchCoverGeniusPolicy(policyId);
                if (!returnData) console.log('  ✓ Policy data retrieved successfully');
            } catch (coverGeniusError) {
                const errorMessage = coverGeniusError instanceof Error ? coverGeniusError.message : 'Unknown Cover Genius API error';
                if (!returnData) {
                    console.error(`  ❌ Cover Genius API call failed for policy ${policyId}:`, errorMessage);
                }

                if (returnData) {
                    return {
                        pnrNumber,
                        policyId,
                        insuranceRecords: [],
                        policyStartDate: null,
                        policyEndDate: null,
                        summary: {
                            totalRecords: 0,
                            withConfirmation: 0,
                            missingConfirmation: 0,
                            withinPolicyPeriod: 0,
                        },
                        sqlQueries: [],
                        error: `Cover Genius API failed: ${errorMessage}`,
                        errorType: 'COVER_GENIUS_API_ERROR'
                    };
                } else {
                    // For CLI mode, throw the error to be caught by outer try-catch
                    throw coverGeniusError;
                }
            }

            // Extract and log policy information
            let policyStartDate = null;
            let policyEndDate = null;
            if (policyResponse && policyResponse.data) {
                if (policyResponse.data.quotes && policyResponse.data.quotes.length > 0) {
                    const quote = policyResponse.data.quotes[0];
                    if (quote.policy_start_date) policyStartDate = moment(quote.policy_start_date);
                    if (quote.policy_end_date) policyEndDate = moment(quote.policy_end_date);
                } else if (policyResponse.data.policy) {
                    const policy = policyResponse.data.policy;
                    if (policy.start_date) policyStartDate = moment(policy.start_date);
                    if (policy.end_date) policyEndDate = moment(policy.end_date);
                }
            } else if (policyResponse && policyResponse.quotes && policyResponse.quotes.length > 0) {
                const quote = policyResponse.quotes[0];
                if (quote.policy_start_date) policyStartDate = moment(quote.policy_start_date);
                if (quote.policy_end_date) policyEndDate = moment(quote.policy_end_date);
            }

            if (!returnData && policyStartDate && policyEndDate) {
                console.log(`  - Policy period: ${policyStartDate.format("YYYY-MM-DD HH:mm:ss")} to ${policyEndDate.format("YYYY-MM-DD HH:mm:ss")}`);
            }

            // Process the data
            if (!returnData) console.log('  - Processing insurance data...');
            const processedData = processInsuranceData(pnrData, policyResponse, policyId);
            if (!returnData) console.log(`  ✓ Processed ${processedData.insuranceRecords.length} insurance records`);

            // Log detailed analysis
            const missingConfirmations = processedData.insuranceRecords.filter(record => !record.hasConfirmation);
            const recordsWithMatchingPolicyId = processedData.insuranceRecords.filter(r => r.matchesCoverGeniusPolicyId);
            const recordsWithinPolicyPeriod = processedData.insuranceRecords.filter(r => r.withinPolicyPeriod === true);

            if (!returnData) {
                console.log(`  - Records missing confirmation: ${missingConfirmations.length}`);
                console.log(`  - Records matching policy ID: ${recordsWithMatchingPolicyId.length}`);
                console.log(`  - Records within policy period: ${recordsWithinPolicyPeriod.length}`);
            }

            // Generate SQL queries for missing confirmations
            const sqlQueries = missingConfirmations.map(record => ({
                recordNumber: record.recordNumber,
                passenger: record.passenger,
                query: `UPDATE P_FZ.RESERVATION_SEGS SET INSURANCE_CONF_NUM='${policyId}' WHERE CONFIRMATION_NUM='${pnrNumber}' AND RECORD_NUM=${record.recordNumber};`
            }));

            if (returnData) {
                return {
                    pnrNumber,
                    policyId,
                    insuranceRecords: processedData.insuranceRecords,
                    policyStartDate: policyStartDate ? policyStartDate.format('YYYY-MM-DD') : null,
                    policyEndDate: policyEndDate ? policyEndDate.format('YYYY-MM-DD') : null,
                    summary: {
                        totalRecords: processedData.insuranceRecords.length,
                        withConfirmation: processedData.insuranceRecords.length - missingConfirmations.length,
                        missingConfirmation: missingConfirmations.length,
                        withinPolicyPeriod: recordsWithinPolicyPeriod.length,
                    },
                    sqlQueries
                };
            }

            // Generate report (only for CLI mode)
            console.log('  - Generating HTML report...');
            generateHtmlReport(
                pnrNumber,
                processedData.insuranceRecords,
                processedData.policyStartDate,
                processedData.policyEndDate,
                policyId
            );
            console.log(`  ✓ Report generated for policy ${policyId}`);

        } catch (error) {
            if (!returnData) {
                console.error(`  ❌ Error processing policy ${policyId}:`, error.message);
                console.log(`  Skipping policy ${policyId} and continuing...`);
            }

            if (returnData) {
                throw error; // Re-throw for API to handle
            }
        }

        if (!returnData) {
            console.log('');
            console.log('='.repeat(60));
            console.log('Process completed successfully!');
            console.log('='.repeat(60));
        }

    } catch (error) {
        if (!returnData) {
            console.error('❌ Fatal error in main process:', error.message);
            console.error('Stack trace:', error.stack);
            process.exit(1);
        } else {
            // For API mode, throw the error to be handled by the API route
            throw error;
        }
    }
}

/**
 * Entry point - handles command line arguments
 */
function run() {
    // Get PNR from command line arguments
    const args = process.argv.slice(2);

    if (args.length === 0) {
        console.log('Usage: node main.js <PNR_NUMBER>');
        console.log('Example: node main.js ABC123');
        process.exit(1);
    }

    const pnrNumber = args[0].toUpperCase().trim();

    if (!pnrNumber) {
        console.error('Error: PNR number cannot be empty');
        process.exit(1);
    }

    // Validate PNR format (basic validation)
    if (pnrNumber.length < 3 || pnrNumber.length > 10) {
        console.error('Error: PNR number should be between 3 and 10 characters');
        process.exit(1);
    }

    // Start the main process
    main(pnrNumber);
}

// Export functions for testing
module.exports = {
    main
};

// Run the application if this file is executed directly
if (require.main === module) {
    run();
}
